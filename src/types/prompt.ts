// Prompt-related types and interfaces

import { AIModel } from '@/enums';

export interface Prompt {
  id: string;
  title: string;
  author: string;
  model: AIModel;
  votes: number;
  thumbnail?: string;
  isPremium?: boolean;
  price?: number;
  isUpvoted: boolean;
}

export type TabValue = 'trending' | 'newest';

// Event handler types
export type VoteHandler = (id: string) => void;
export type PromptClickHandler = (id: string) => void;
export type ModelFilterHandler = (model: AIModel | 'All') => void;

// Filter function type
export type PromptFilter = (prompts: Prompt[]) => Prompt[];
