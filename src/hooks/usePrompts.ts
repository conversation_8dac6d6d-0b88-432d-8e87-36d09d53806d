import { NEWEST_PROMPTS_DATA, TRENDING_PROMPTS_DATA } from '@/data/prompts';
import { AIModel } from '@/enums';
import {
  ModelFilterHandler,
  Prompt,
  PromptFilter,
  VoteHandler
} from '@/types/prompt';
import { useCallback, useMemo, useState } from 'react';

interface UsePromptsReturn {
  // Data
  trendingPrompts: Prompt[];
  newestPrompts: Prompt[];
  filteredTrendingPrompts: Prompt[];
  filteredNewestPrompts: Prompt[];

  // State
  searchQuery: string;
  selectedModel: AIModel | 'All';
  votes: Record<string, number>;

  // Actions
  setSearchQuery: (query: string) => void;
  handleUpvote: VoteHandler;
  handleDownvote: VoteHandler;
  handleModelFilter: ModelFilterHandler;
  handleSearch: () => void;
}

export function usePrompts(): UsePromptsReturn {
  // State
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedModel, setSelectedModel] = useState<AIModel | 'All'>('All');
  const [votes, setVotes] = useState<Record<string, number>>({});

  // Memoized data with votes
  const trendingPrompts = useMemo(() => TRENDING_PROMPTS_DATA, []);
  const newestPrompts = useMemo(() => NEWEST_PROMPTS_DATA, []);

  // Filter function
  const filterPrompts: PromptFilter = useCallback(
    (prompts: Prompt[]) => {
      return prompts.filter((prompt) => {
        const matchesSearch =
          !searchQuery ||
          prompt.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          prompt.author.toLowerCase().includes(searchQuery.toLowerCase()) ||
          prompt.model.toLowerCase().includes(searchQuery.toLowerCase());

        const matchesModel =
          selectedModel === 'All' || prompt.model === selectedModel;

        return matchesSearch && matchesModel;
      });
    },
    [searchQuery, selectedModel]
  );

  // Filtered data
  const filteredTrendingPrompts = useMemo(
    () => filterPrompts(trendingPrompts),
    [filterPrompts, trendingPrompts]
  );

  const filteredNewestPrompts = useMemo(
    () => filterPrompts(newestPrompts),
    [filterPrompts, newestPrompts]
  );

  // Event handlers
  const handleUpvote: VoteHandler = useCallback((id: string) => {
    setVotes((prev) => ({ ...prev, [id]: (prev[id] || 0) + 1 }));
  }, []);

  const handleDownvote: VoteHandler = useCallback((id: string) => {
    setVotes((prev) => ({ ...prev, [id]: Math.max((prev[id] || 0) - 1, 0) }));
  }, []);

  const handleModelFilter: ModelFilterHandler = useCallback(
    (model: AIModel | 'All') => {
      setSelectedModel(model);
    },
    []
  );

  const handleSearch = useCallback(() => {
    // In a real app, this would trigger an API call
    console.log('Searching for:', searchQuery);
  }, [searchQuery]);

  return {
    // Data
    trendingPrompts,
    newestPrompts,
    filteredTrendingPrompts,
    filteredNewestPrompts,

    // State
    searchQuery,
    selectedModel,
    votes,

    // Actions
    setSearchQuery,
    handleUpvote,
    handleDownvote,
    handleModelFilter,
    handleSearch
  };
}
