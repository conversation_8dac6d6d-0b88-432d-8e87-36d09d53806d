'use client';

import { ClientOnly } from '@/components';
import { Button } from '@/components/ui/button';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger
} from '@/components/ui/sheet';
import { ScreenRoute } from '@/enums';
import { useTheme } from '@/hooks';
import {
  DesktopNavigationProps,
  LogoProps,
  MobileMenuProps,
  NavigationButtonProps,
  NavigationItem,
  ThemeToggleProps
} from '@/types';
import { cn } from '@/utils';
import {
  Home,
  Menu,
  Monitor,
  Moon,
  Settings,
  Sun,
  TrendingUp,
  Upload,
  User
} from 'lucide-react';
import { usePathname, useRouter } from 'next/navigation';
import { useState } from 'react';

// Constants
const NAVIGATION_ITEMS: NavigationItem[] = [
  { id: ScreenRoute.Home, label: 'Home', icon: Home, path: '/' },
  {
    id: ScreenRoute.Upload,
    label: 'Upload Prompt',
    icon: Upload,
    path: '/upload'
  },
  { id: ScreenRoute.Profile, label: 'Profile', icon: User, path: '/profile' },
  {
    id: ScreenRoute.Settings,
    label: 'Settings',
    icon: Settings,
    path: '/settings'
  }
];

const USER_INFO = {
  name: 'Alex Chen',
  username: '@alexcreator'
};

// Utility functions
function getCurrentScreen(pathname: string): ScreenRoute {
  return (
    NAVIGATION_ITEMS.find((item) => item.path === pathname)?.id ||
    ScreenRoute.Home
  );
}

function Logo(props: LogoProps) {
  const { onClick } = props;

  return (
    <div
      className='flex items-center space-x-2 cursor-pointer theme-transition hover:scale-105'
      onClick={onClick}
    >
      <div className='w-8 h-8 gradient-purple-cyan rounded-lg flex items-center justify-center shadow-glow'>
        <TrendingUp className='w-5 h-5 text-white' />
      </div>
      <span className='text-xl font-semibold text-gradient'>PromptRank</span>
    </div>
  );
}

function ThemeToggleFallback() {
  return (
    <Button
      variant='ghost'
      size='sm'
      className='btn-light-enhanced hover:shadow-glow theme-transition p-3 rounded-full hover:text-primary'
    >
      <Monitor className='w-5 h-5' />
    </Button>
  );
}

function ThemeToggle(props: ThemeToggleProps) {
  const { isDarkMode, isMobile, onToggle } = props;

  if (isMobile) {
    return (
      <Button
        variant='ghost'
        className='hover:shadow-glow theme-transition justify-start w-full py-3 hover:bg-accent'
        onClick={onToggle}
      >
        {isDarkMode ? (
          <>
            <Sun className='w-5 h-5 mr-3 text-yellow-500' />
            Light Mode
          </>
        ) : (
          <>
            <Moon className='w-5 h-5 mr-3 text-indigo-600' />
            Dark Mode
          </>
        )}
      </Button>
    );
  }

  return (
    <Button
      variant='ghost'
      size='sm'
      className='btn-light-enhanced hover:shadow-glow theme-transition p-3 rounded-full hover:text-primary'
      onClick={onToggle}
    >
      {isDarkMode ? (
        <Sun className='w-5 h-5 text-yellow-500' />
      ) : (
        <Moon className='w-5 h-5 text-indigo-600' />
      )}
    </Button>
  );
}

function NavigationButton(props: NavigationButtonProps) {
  const { item, isActive, isMobile, onClick } = props;
  const { icon: Icon, label } = item;

  if (isMobile) {
    return (
      <Button
        variant={isActive ? 'default' : 'ghost'}
        className={cn(
          'hover:shadow-glow theme-transition justify-start w-full py-3',
          isActive && 'shadow-glow'
        )}
        onClick={onClick}
      >
        <Icon className='w-5 h-5 mr-3' />
        {label}
      </Button>
    );
  }

  return (
    <Button
      variant={isActive ? 'default' : 'ghost'}
      size='sm'
      className={cn(
        'hover:shadow-glow theme-transition p-3 rounded-full',
        isActive && 'shadow-glow',
        !isActive && 'btn-light-enhanced'
      )}
      onClick={onClick}
      title={label}
    >
      <Icon className='w-5 h-5' />
    </Button>
  );
}

function DesktopNavigation(props: DesktopNavigationProps) {
  const { currentScreen, onNavigate, isDarkMode, onThemeToggle } = props;

  const desktopNavItems = NAVIGATION_ITEMS.filter((item) => item.id !== 'home');

  return (
    <div className='hidden md:flex items-center space-x-2'>
      {desktopNavItems.map((item) => (
        <NavigationButton
          key={item.id}
          item={item}
          isActive={currentScreen === item.id}
          onClick={() => onNavigate(item.id)}
        />
      ))}
      <ClientOnly fallback={<ThemeToggleFallback />}>
        <ThemeToggle isDarkMode={isDarkMode} onToggle={onThemeToggle} />
      </ClientOnly>
    </div>
  );
}

function MobileMenu(props: MobileMenuProps) {
  const {
    isOpen,
    currentScreen,
    isDarkMode,
    userInfo,
    onNavigate,
    onOpenChange,
    onThemeToggle
  } = props;

  const handleNavigation = (screen: ScreenRoute): void => {
    onNavigate(screen);
    onOpenChange(false);
  };

  return (
    <div className='md:hidden'>
      <Sheet open={isOpen} onOpenChange={onOpenChange}>
        <SheetTrigger asChild>
          <Button
            variant='ghost'
            size='sm'
            className='btn-light-enhanced p-3 rounded-full'
          >
            <Menu className='w-5 h-5' />
          </Button>
        </SheetTrigger>
        <SheetContent
          side='right'
          className='glass theme-transition border-border w-full'
        >
          <SheetHeader>
            <SheetTitle className='text-lg font-semibold text-gradient'>
              Menu
            </SheetTitle>
            <SheetDescription className='text-sm text-muted-foreground'>
              Navigate and manage your settings
            </SheetDescription>
          </SheetHeader>

          <div className='flex flex-col space-y-4 p-5'>
            {/* Mobile Navigation Items */}
            {NAVIGATION_ITEMS.map((item) => (
              <NavigationButton
                key={item.id}
                item={item}
                isActive={currentScreen === item.id}
                onClick={() => handleNavigation(item.id)}
                isMobile
              />
            ))}

            {/* Dark Mode Toggle */}
            <div className='pt-4 border-t border-border'>
              <ClientOnly fallback={<ThemeToggleFallback />}>
                <ThemeToggle
                  isDarkMode={isDarkMode}
                  onToggle={onThemeToggle}
                  isMobile
                />
              </ClientOnly>
            </div>

            {/* User Info Section */}
            <div className='pt-4 border-t border-border'>
              <div className='glass-strong rounded-lg p-4 theme-transition'>
                <div className='flex items-center space-x-3'>
                  <div className='w-10 h-10 gradient-purple-cyan rounded-full flex items-center justify-center shadow-glow'>
                    <User className='w-5 h-5 text-white' />
                  </div>
                  <div>
                    <p className='font-medium'>{userInfo.name}</p>
                    <p className='text-sm text-muted-foreground'>
                      {userInfo.username}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
}

export function Header() {
  const router = useRouter();
  const pathname = usePathname();

  const { isDarkMode, toggleTheme } = useTheme();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const currentScreen = getCurrentScreen(pathname); // returns ScreenRoute

  const handleNavigation = (screen: ScreenRoute): void => {
    // Use ScreenRoute
    const href = screen === ScreenRoute.Home ? '/' : `/${screen}`;
    router.push(href);
  };

  const handleLogoClick = (): void => {
    handleNavigation(ScreenRoute.Home);
  };

  return (
    <nav className='fixed top-0 left-0 right-0 z-50 glass border-b theme-transition px-4 md:px-6 h-16'>
      <div className='max-w-7xl mx-auto flex items-center justify-between h-full'>
        {/* Logo */}
        <Logo onClick={handleLogoClick} />

        {/* Desktop Navigation */}
        <DesktopNavigation
          currentScreen={currentScreen} // Now ScreenRoute
          onNavigate={handleNavigation}
          isDarkMode={isDarkMode}
          onThemeToggle={toggleTheme}
        />

        {/* Mobile Menu */}
        <MobileMenu
          isOpen={isMobileMenuOpen}
          onOpenChange={setIsMobileMenuOpen}
          currentScreen={currentScreen}
          onNavigate={handleNavigation}
          isDarkMode={isDarkMode}
          onThemeToggle={toggleTheme}
          userInfo={USER_INFO}
        />
      </div>
    </nav>
  );
}
