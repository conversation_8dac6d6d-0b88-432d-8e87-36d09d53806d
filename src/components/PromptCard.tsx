import { ImageWithFallback } from '@/components';
import { VoteHandler } from '@/types';
import { ChevronDown, ChevronUp, Zap } from 'lucide-react';
import { Badge } from './ui/badge';
import { Button } from './ui/button';

interface PromptCardProps {
  id: string;
  title: string;
  author: string;
  model: string;
  votes?: number;
  thumbnail?: string;
  isPremium?: boolean;
  price?: number;
  isUpvoted?: boolean;
  onUpvote: VoteHandler;
  onDownvote: VoteHandler;
  onClick?: (id: string) => void;
}

export function PromptCard(props: PromptCardProps) {
  const {
    id,
    title,
    author,
    model,
    votes,
    thumbnail,
    isPremium = false,
    price,
    isUpvoted = false,
    onUpvote,
    onDownvote,
    onClick
  } = props;

  return (
    <div
      className='card-light rounded-xl p-6 hover:card-light cursor-pointer group theme-transition'
      onClick={() => onClick?.(id)}
    >
      <div className='flex gap-4 h-full'>
        <div className='flex flex-col items-center space-y-2'>
          <Button
            variant='ghost'
            size='sm'
            className={`p-1 h-8 w-8 ${
              isUpvoted ? 'text-purple-400' : 'text-muted-foreground'
            } hover:text-purple-400 transition-colors rounded-full`}
            onClick={(e) => {
              e.stopPropagation();
              onUpvote?.(id);
            }}
          >
            <ChevronUp className='w-4 h-4' />
          </Button>
          <span
            className={`text-sm font-medium ${
              !!votes ? 'text-green-500' : 'text-muted-foreground'
            }`}
          >
            {votes}
          </span>
          <Button
            variant='ghost'
            size='sm'
            className='p-1 h-8 w-8 text-muted-foreground hover:text-red-400 transition-colors rounded-full'
            onClick={(e) => {
              e.stopPropagation();
              onDownvote?.(id);
            }}
          >
            <ChevronDown className='w-4 h-4' />
          </Button>
        </div>

        <div className='flex flex-col flex-1 gap-4 h-full'>
          <div className='w-ful aspect-video rounded-lg overflow-hidden shadow-soft relative'>
            <ImageWithFallback
              src={`https://images.unsplash.com/photo-${thumbnail}?w=400&h=300&fit=crop`}
              alt={title}
              fill
              sizes='(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
              className='object-cover'
              priority={false}
            />
          </div>
          <div className='flex items-start justify-between'>
            <h3 className='font-semibold text-lg group-hover:text-primary transition-colors'>
              {title}
            </h3>
          </div>
          <div className='flex flex-col gap-4'>
            {isPremium && (
              <div className='flex items-center space-x-2'>
                <Badge className='gradient-purple-cyan text-white border-0 shadow-soft'>
                  <Zap className='w-3 h-3 mr-1' />
                  Premium
                </Badge>
                {price && (
                  <span className='text-sm font-medium text-green-500'>
                    ${price}
                  </span>
                )}
              </div>
            )}
            <div className='flex items-center justify-between'>
              <div className='flex items-center space-x-3'>
                <span className='text-sm text-muted-foreground'>
                  by {author}
                </span>
                <Badge variant='secondary' className='glass border-light'>
                  {model}
                </Badge>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
