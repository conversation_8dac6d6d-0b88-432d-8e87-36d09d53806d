import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search } from 'lucide-react';

interface SearchBarProps {
  searchQuery: string;
  onSearchQueryChange: (query: string) => void;
  onSearch: () => void;
  placeholder?: string;
  className?: string;
}

export function SearchBar({
  searchQuery,
  onSearchQueryChange,
  onSearch,
  placeholder = 'Search for prompts, creators, or AI models...',
  className = ''
}: SearchBarProps) {
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      onSearch();
    }
  };

  return (
    <div className={`max-w-2xl mx-auto relative ${className}`}>
      <div className='glass rounded-full p-2 flex items-center'>
        <Search className='w-5 h-5 text-muted-foreground ml-4' />
        <Input
          placeholder={placeholder}
          value={searchQuery}
          onChange={(e) => onSearchQueryChange(e.target.value)}
          onKeyDown={handleKeyDown}
          className='border-0 bg-transparent flex-1 px-4 focus:outline-none focus:ring-0'
        />
        <Button 
          onClick={onSearch}
          className='gradient-purple-cyan text-white border-0 rounded-full shadow-glow'
        >
          Search
        </Button>
      </div>
    </div>
  );
}
