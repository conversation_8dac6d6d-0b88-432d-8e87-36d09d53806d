import { Button } from '@/components/ui/button';
import { AIModel, AI_MODELS } from '@/types';
import { Filter } from 'lucide-react';

interface ModelFilterProps {
  selectedModel: AIModel | 'All';
  onModelFilter: (model: AIModel | 'All') => void;
  className?: string;
}

export function ModelFilter({
  selectedModel,
  onModelFilter,
  className = ''
}: ModelFilterProps) {
  return (
    <div className={`flex flex-wrap gap-3 justify-center ${className}`}>
      <Button
        variant={selectedModel === 'All' ? 'default' : 'outline'}
        className='glass hover:shadow-glow transition-all'
        onClick={() => onModelFilter('All')}
      >
        <Filter className='w-4 h-4 mr-2' />
        All Models
      </Button>
      {AI_MODELS.map((model) => (
        <Button
          key={model}
          variant={selectedModel === model ? 'default' : 'outline'}
          className='glass hover:shadow-glow transition-all'
          onClick={() => onModelFilter(model)}
        >
          {model}
        </Button>
      ))}
    </div>
  );
}
