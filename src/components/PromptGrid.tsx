import { PromptCard } from '@/components';
import { Prompt, Vote<PERSON><PERSON><PERSON>, PromptClickHandler } from '@/types';

interface PromptGridProps {
  prompts: Prompt[];
  onUpvote: VoteHandler;
  onDownvote: VoteHandler;
  onClick: PromptClickHandler;
  emptyMessage?: string;
  className?: string;
}

export function PromptGrid({
  prompts,
  onUpvote,
  onDownvote,
  onClick,
  emptyMessage = 'No prompts found matching your criteria.',
  className = ''
}: PromptGridProps) {
  if (prompts.length === 0) {
    return (
      <div className='text-center py-12'>
        <p className='text-muted-foreground'>{emptyMessage}</p>
      </div>
    );
  }

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}>
      {prompts.map((prompt) => (
        <PromptCard
          key={prompt.id}
          {...prompt}
          onUpvote={onUpvote}
          onDownvote={onDownvote}
          onClick={onClick}
        />
      ))}
    </div>
  );
}
